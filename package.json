{"name": "pgfe-front", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "test:unit": "vitest", "build-only": "vite build", "type-check": "vue-tsc --build", "lint:oxlint": "oxlint . --fix -D correctness --ignore-path .gitignore", "lint:eslint": "eslint . --fix", "lint": "run-s lint:*", "format": "prettier --write src/"}, "dependencies": {"@tailwindcss/vite": "^4.1.10", "@tanstack/vue-table": "^8.21.3", "@vee-validate/zod": "^4.15.1", "@vueuse/core": "^13.3.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-vue-next": "^0.516.0", "pinia": "^3.0.1", "reka-ui": "^2.3.1", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.10", "tw-animate-css": "^1.3.4", "vaul-vue": "^0.4.1", "vee-validate": "^4.15.1", "vue": "^3.5.13", "vue-router": "^4.5.0", "zod": "^3.25.67"}, "devDependencies": {"@iconify-json/hugeicons": "^1.2.5", "@iconify-json/lucide": "^1.2.54", "@iconify/tailwind4": "^1.0.6", "@iconify/vue": "^5.0.0", "@tsconfig/node22": "^22.0.1", "@types/jsdom": "^21.1.7", "@types/node": "^22.14.0", "@vitejs/plugin-vue": "^5.2.3", "@vitejs/plugin-vue-jsx": "^4.1.2", "@vitest/eslint-plugin": "^1.1.39", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "eslint": "^9.22.0", "eslint-plugin-oxlint": "^0.16.0", "eslint-plugin-vue": "~10.0.0", "jiti": "^2.4.2", "jsdom": "^26.0.0", "npm-run-all2": "^7.0.2", "oxlint": "^0.16.0", "prettier": "3.5.3", "typescript": "~5.8.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vitest": "^3.1.1", "vue-tsc": "^2.2.8"}, "packageManager": "pnpm@10.11.1+sha512.e519b9f7639869dc8d5c3c5dfef73b3f091094b0a006d7317353c72b124e80e1afd429732e28705ad6bfa1ee879c1fce46c128ccebd3192101f43dd67c667912"}