<script setup lang="ts">
import RightDashHeader from "@/components/atoms/RightDashHeader.vue"
import { modulesNavigation } from "@/data/navigations";
import { cn } from "@/lib/utils";

const props = defineProps<{
  moduleName: keyof typeof modulesNavigation,
  activeRoute: string
}>()

const items = modulesNavigation[props.moduleName]
</script>

<template>
  <header class="flex items-center mx-auto max-w-7xl h-20 justify-between gap-5">
    <router-link to="#">
      <img src="/pgfe-logo.png" alt="pattern background" class="size-18 object-cover" />
    </router-link>
    <div class="flex flex-1">
      <ul class="w-max bg-white shadow-lg shadow-gray-100/20 p-0.5 rounded-full flex items-center space-x-1">
        <li v-for="item in items" :key="item.id" class="">
          <RouterLink :to="item.link" :class="cn(
            'flex items-center gap-1 px-4 py-3 rounded-full text-foreground-muted ease-linear transition-colors', {
            'bg-primary text-white': item.link === activeRoute,
            'hover:bg-muted':item.link !== activeRoute
          }
          )">
            <span v-if="item.link === activeRoute" :class="['iconify flex text-xl', item.icon]" />
            <span>
              {{ item.text }}
            </span>
          </RouterLink>
        </li>
      </ul>
    </div>
    <RightDashHeader />
  </header>
</template>