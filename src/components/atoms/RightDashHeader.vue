<script setup lang="ts">
import { Icon } from '@iconify/vue';


</script>
<template>
    <div class="flex items-center gap-3">
        <button class="size-11 flex items-center justify-center rounded-full bg-white shadow-sm">
            <span class="size-5 iconify hugeicons--setting-07" />
        </button>
        <button class="size-11 flex items-center justify-center rounded-full bg-white shadow-sm">
            <span class="size-5 iconify hugeicons--notification-03" />
        </button>
        <div class="flex items-center gap-1">
            <div class="size-11 flex items-center justify-center rounded-full bg-white shadow-sm">
                <span class="size-5 iconify hugeicons--user" />
            </div>
            <div class="hidden sm:flex flex-col">
                <span class="font-medium text-foreground-subtitle">Jack <PERSON></span>
                <span class="text-sm text-foreground-muted">Administrateur</span>
            </div>
            <button class="size-11 flex items-center justify-center rounded-full bg-white text-red-600 shadow-sm">
                <span class="size-5 iconify hugeicons--logout-04" />
            </button>
        </div>
    </div>
</template>