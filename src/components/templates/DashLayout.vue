<script setup lang="ts">
import DashNavbar from '../molecules/DashNavbar.vue';
import { modulesNavigation } from '@/data/navigations';

defineProps<{
    moduleName: keyof typeof modulesNavigation,
    activeRoute: string
}>()
</script>
<template>
    <div class="w-full min-h-dvh bg-dashboard-bg">
        <DashNavbar :activeRoute="activeRoute" :module-name="moduleName" />
        <main>
            <slot />
        </main>
    </div>
</template>