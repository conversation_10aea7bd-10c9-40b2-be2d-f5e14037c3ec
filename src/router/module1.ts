import type { RouteRecordRaw } from 'vue-router'

const module1Routes: RouteRecordRaw[] = [
  {
    path:"/apprenants",
    name:"apprenants-home-module",
    component:()=>import('./../app/m-student/HomeModule1.vue')
  },
    
  
  {
    path:"/apprenants/saisie-prealable",
    name:"apprenants-module-saisie",
    component:()=>import('./../app/m-student/saisie-prealable/MainSaisiPrealable.vue')
  },
      {
    path:"/apprenants/saisie-prealable/classes",
    name:"apprenants-module-saisie-classes",
    component:()=>import('./../app/m-student/saisie-prealable/SaisiClasse.vue')
  },
      {
    path:"/apprenants/saisie-prealable/cours",
    name:"apprenants-module-saisie-cours",
    component:()=>import('./../app/m-student/saisie-prealable/SaisiCours.vue')
  },



  {
    path:"/apprenants/saisie-operation",
    name:"apprenants-module-saisie-operation",
    component:()=>import('./../app/m-student/HomeModule1.vue')
  },
  
  {
    path:"/apprenants/rapport",
    name:"apprenants-module-rapport",
    component:()=>import('./../app/m-student/HomeModule1.vue')
  },
]

export {module1Routes}