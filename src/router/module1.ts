import type { RouteRecordRaw } from 'vue-router'

const module1Routes: RouteRecordRaw[] = [
  {
    path:"/apprenants",
    name:"apprenants-home-module",
    component:()=>import('../app/formel/student/HomeModule1.vue')
  },
    
  
  {
    path:"/apprenants/saisie-prealable",
    name:"apprenants-module-saisie",
    component:()=>import('../app/formel/student/saisie-prealable/MainSaisiPrealable.vue')
  },
      {
    path:"/apprenants/saisie-prealable/classes",
    name:"apprenants-module-saisie-classes",
    component:()=>import('../app/formel/student/saisie-prealable/SaisiClasse.vue')
  },
      {
    path:"/apprenants/saisie-prealable/cours",
    name:"apprenants-module-saisie-cours",
    component:()=>import('../app/formel/student/saisie-prealable/SaisiCours.vue')
  },



  {
    path:"/apprenants/operations",
    name:"apprenants-module-saisie-operation",
    component:()=>import('../app/formel/student/operations/MainSaisieOperations.vue')
  },
  
  {
    path:"/apprenants/operations/presences",
    name:"apprenants-module-presence",
    component:()=>import('../app/formel/student/operations/StudentsPresence.vue')
  },
]

export {module1Routes}