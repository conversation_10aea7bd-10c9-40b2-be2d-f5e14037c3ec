<template>
    <div class="min-h-screen flex items-center justify-center p-4 relative">
        <div class="fixed inset-0">
            <img src="/screen-pattern.png" alt="pattern background" class="size-full object-cover">
        </div>
        <div class="w-full max-w-6xl flex flex-col lg:flex-row items-center gap-8 lg:gap-16 relative">
    
            <div class="flex-1 space-y-6 text-center lg:text-left">
                <!-- Logo -->
                <div class="flex justify-center max-w-md">
                    <img src="/pgfe-logo.png" width="500" class="h-24 w-auto" />
                </div>

                <!-- Main Title -->
                <div class="max-w-md">
                    <h1 class="text-3xl lg:text-4xl font-bold text-gray-800 leading-tight">
                        PROGICIEL DE GESTION<br>
                        FORMATION EMPLOI
                    </h1>
                </div>

                <!-- Description -->
                <div class="space-y-4 max-w-md text-foreground text-sm leading-relaxed mx-auto lg:mx-0">
                    <p>
                        PGFER est un progiciel qui vous permet la gestion, la centralisation et la
                        mesure de vos informations à partir de vos actions.
                    </p>
                    <p>
                        c'est un outil complet pour les écoles, le centre de formation, mais
                        également pour le suivi de l'insertion professionnelle des apprenants, tout
                        en mettant aussi un point sur les personnels.
                    </p>
                </div>
            </div>

            <!-- Right Side - Login Form -->
            <div class="w-full max-w-md">
                <Card class="bg-white rounded-2xl p-6 lg:p-8">
                    <!-- Form Header -->
                    <div class="mb-6">
                        <h2 class="text-2xl font-bold text-gray-800 mb-2">Inscrivez-vous</h2>
                        <p class="text-gray-500 text-sm">saisissez les coordonnées</p>
                    </div>

                    <!-- Form -->
                    <form class="space-y-5">
                        <!-- Username Field -->
                        <div>
                            <label for="username" class="block text-sm font-medium text-gray-700 mb-2">
                                Nom d'utilisateur
                            </label>
                            <div class="relative">
                                <Input type="text" id="username" name="username" placeholder="utilisateur"
                                    class="w-full pr-10 border border-gray-200 transition-all"/>
                                <div class="absolute right-3 top-1/2 transform -translate-y-1/2">
                                    <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                    </svg>
                                </div>
                            </div>
                        </div>

                        <!-- Password Field -->
                        <div>
                            <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                                Mot de passe
                            </label>
                            <div class="relative">
                                <Input type="password" id="password" name="password" placeholder="••••••••"
                                    class="w-full pr-10  outline-none transition-all"/>
                                <button type="button" class="absolute right-3 top-1/2 transform -translate-y-1/2"
                                    onclick="togglePassword()">
                                    <svg id="eyeIcon" class="w-5 h-5 text-blue-400" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                    </svg>
                                </button>
                            </div>
                            <p class="text-xs text-gray-500 mt-2">
                                utilisez au moins 8 caractères, en combinant lettres, chiffres et symboles.
                            </p>
                        </div>

                        <!-- Submit Button -->
                        <Button type="submit"
                            class="w-full ">
                            Inscrivez-vous
                        </Button>

                        <!-- Forgot Password Link -->
                        <div class="text-center pt-2">
                            <a href="#"
                                class="text-blue-500 hover:text-blue-600 text-sm transition-colors duration-200">
                                mot de passe oublié ?
                            </a>
                        </div>
                    </form>
                </Card>
            </div>
        </div>
    </div>

</template>

<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';


</script>