<script setup lang="ts">
import DashLayout from '@/components/templates/DashLayout.vue';
import DashPageHeader from '@/components/templates/DashPageHeader.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { tagNavSaisieNonFormel } from './tags-nav';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table'
import { Checkbox } from '@/components/ui/checkbox';
import {
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select'



const formateursResponsables = [
    {
        id: 1,
        annee: "2023-2024",
        formation: "Lettres Modernes",
        formateur: "Dr. <PERSON>"
    },
    {
        id: 2,
        annee: "2023-2024",
        formation: "Sciences Physiques",
        formateur: "<PERSON><PERSON>"
    },
    {
        id: 3,
        annee: "2024-2025",
        formation: "Mathématiques",
        formateur: "<PERSON><PERSON> <PERSON>"
    },
]

const annees = [
    { value: "2023-2024", label: "2023-2024" },
    { value: "2024-2025", label: "2024-2025" },
    { value: "2025-2026", label: "2025-2026" }
]

const formations = [
    { value: "lettres-modernes", label: "Lettres Modernes" },
    { value: "sciences-physiques", label: "Sciences Physiques" },
    { value: "mathematiques", label: "Mathématiques" },
    { value: "histoire-geographie", label: "Histoire-Géographie" },
    { value: "langues-vivantes", label: "Langues Vivantes" }
]

const formateurs = [
    { value: "martin-dubois", label: "Dr. Martin Dubois" },
    { value: "marie-leclerc", label: "Prof. Marie Leclerc" },
    { value: "pierre-rousseau", label: "Dr. Pierre Rousseau" },
    { value: "sophie-bernard", label: "Prof. Sophie Bernard" },
    { value: "jean-moreau", label: "Dr. Jean Moreau" }
]
</script>

<template>
    <DashLayout active-route="/apprenants/saisie-prealable" module-name="students">
        <div class="pb-6 mx-auto w-full max-w-6xl">
            <DashPageHeader title="Enseignement formel" :tags="tagNavSaisieNonFormel" active-tag-name="classes" />
            <div
                class="mt-10 bg-white min-h-[72vh] rounded-3xl shadow-lg shadow-gray-100/20 p-3 grid md:grid-cols-[1fr_250px] xl:grid-cols-[1fr_320px] gap-4 md:gap-10">
                <div class="rounded-xl bg-muted p-2.5">
                    <div class="relative max-w-sm">
                        <Input type="text" id="search" name="search" placeholder="Rechercher un formateur..."
                            class="w-full ps-10 border border-gray-200/40 bg-white transition-all h-10 rounded-full" />
                        <div class="absolute left-3 top-1/2 transform -translate-y-1/2 text-foreground-muted/70">
                            <span class="flex iconify hugeicons--search-01 text-sm"></span>
                        </div>
                    </div>
                    <div class="mt-4 rounded-xl overflow-hidden">
                        <Table class="rounded-xl bg-white">
                            <TableHeader>
                                <TableRow>
                                    <TableHead class="w-[20px]">
                                        <Checkbox class="bg-white scale-70" />
                                    </TableHead>
                                    <TableHead class="text-left">
                                        Année
                                    </TableHead>
                                    <TableHead>
                                        Formation
                                    </TableHead>
                                    <TableHead>
                                        Formateur
                                    </TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                <TableRow v-for="item in formateursResponsables" :key="item.id">
                                    <TableCell class="w-[40px]">
                                        <Checkbox class="bg-white scale-70" />
                                    </TableCell>
                                    <TableCell>
                                        {{ item.annee }}
                                    </TableCell>
                                    <TableCell>
                                        {{ item.formation }}
                                    </TableCell>
                                    <TableCell class="text-left">
                                        {{ item.formateur }}
                                    </TableCell>
                                </TableRow>
                            </TableBody>
                        </Table>
                    </div>
                </div>
                <div class="">
                    <div class="flex items-center gap-3">
                        <Button size="md" variant="outline" class="rounded-full">
                            <span class="flex iconify hugeicons--pdf-02"></span>
                            Exporter pdf
                        </Button>
                        <Button size="md" variant="outline" class="rounded-full">
                            <span class="flex iconify hugeicons--ai-sheets"></span>
                            Exporter Excel
                        </Button>
                    </div>
                    <form action="" class="mt-6 flex flex-col gap-3">
                        <div class="flex flex-col space-y-1.5 flex-1">
                            <Label for="annee" class="text-sm font-medium">
                                Année
                            </Label>
                            <Select id="annee">
                                <SelectTrigger id="annee" class="h-10 w-full">
                                    <SelectValue placeholder="Sélectionner une année" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectGroup>
                                        <SelectItem v-for="annee in annees" :key="annee.value" :value="annee.value">
                                            {{ annee.label }}
                                        </SelectItem>
                                    </SelectGroup>
                                </SelectContent>
                            </Select>
                        </div>
                        <div class="flex flex-col space-y-1.5 flex-1">
                            <Label for="formation" class="text-sm font-medium">
                                Formation
                            </Label>
                            <Select id="formation">
                                <SelectTrigger id="formation" class="h-10 w-full">
                                    <SelectValue placeholder="Sélectionner une formation" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectGroup>
                                        <SelectItem v-for="formation in formations" :key="formation.value"
                                            :value="formation.value">
                                            {{ formation.label }}
                                        </SelectItem>
                                    </SelectGroup>
                                </SelectContent>
                            </Select>
                        </div>
                        <div class="flex flex-col space-y-1.5 flex-1">
                            <Label for="formateur" class="text-sm font-medium">
                                Formateur
                            </Label>
                            <Select id="formateur">
                                <SelectTrigger id="formateur" class="h-10 w-full">
                                    <SelectValue placeholder="Sélectionner un formateur" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectGroup>
                                        <SelectItem v-for="formateur in formateurs" :key="formateur.value"
                                            :value="formateur.value">
                                            {{ formateur.label }}
                                        </SelectItem>
                                    </SelectGroup>
                                </SelectContent>
                            </Select>
                        </div>
                        <div class="mt-1 pb-2">
                            <p class="text-sm text-foreground-muted">
                                * Tous les champs sont obligatoires
                            </p>
                        </div>
                        <Button class="h-10 px-3">
                            <span class="iconify flex hugeicons--floppy-disk "></span>
                            Ajouter
                        </Button>
                    </form>
                </div>
            </div>
        </div>
    </DashLayout>
</template>
