<script setup lang="ts">
import DashLayout from '@/components/templates/DashLayout.vue';
import DashPageHeader from '@/components/templates/DashPageHeader.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { tagNavSaisieNonFormel } from './tags-nav';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table'
import { Checkbox } from '@/components/ui/checkbox';
import {
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select'
import { ref } from 'vue';

const classes = [
    {
        id: 1,
        code: "C1",
        designation: "Classe 1",
        niveau: "1",
        filiere: "Lettres",
        cycle: "Cycle 1"
    },
    {
        id: 2,
        code: "C2",
        designation: "Classe 2",
        niveau: "2",
        filiere: "Lettres",
        cycle: "Cycle 1"
    },
    {
        id: 3,
        code: "C3",
        designation: "Classe 3",
        niveau: "3",
        filiere: "Lettres",
        cycle: "Cycle 1"
    },
]

const formations = [
    { id: 1, code: "FM1", designation: "Plomberie", duree: 12 },
    { id: 2, code: "FM2", designation: "Electricité", duree: 10 },
    { id: 3, code: "FM3", designation: "Menuiserie", duree: 8 },
]

const metiers = [
    { value: "Plomberie", label: "Plomberie" },
    { value: "Electricité", label: "Electricité" },
    { value: "Menuiserie", label: "Menuiserie" },
]

const selectedMetier = ref('');
const duree = ref('');
</script>

<template>
    <DashLayout active-route="/apprenants/saisie-prealable" module-name="students">
        <div class="pb-6 mx-auto w-full max-w-6xl">
            <DashPageHeader title="Enseignement formel" :tags="tagNavSaisieNonFormel" active-tag-name="classes" />
            <div
                class="mt-10 bg-white min-h-[72vh] rounded-3xl shadow-lg shadow-gray-100/20 p-3 grid md:grid-cols-[1fr_250px] xl:grid-cols-[1fr_320px] gap-4 md:gap-10">
                <div class="rounded-xl bg-muted p-2.5">
                    <div class="relative max-w-sm">
                        <Input type="text" id="search" name="search" placeholder="Rechercher une formation..."
                            class="w-full ps-10 border border-gray-200/40 bg-white transition-all h-10 rounded-full" />
                        <div class="absolute left-3 top-1/2 transform -translate-y-1/2 text-foreground-muted/70">
                            <span class="flex iconify hugeicons--search-01 text-sm"></span>
                        </div>
                    </div>
                    <div class="mt-4 rounded-xl overflow-hidden">
                        <Table class="rounded-xl bg-white">
                            <TableHeader>
                                <TableRow>
                                    <TableHead class="w-[20px]">
                                        <Checkbox class="bg-white scale-70" />
                                    </TableHead>
                                    <TableHead class="text-left">
                                        Code
                                    </TableHead>
                                    <TableHead>
                                        Formation-métier
                                    </TableHead>
                                    <TableHead>
                                        Durée (mois)
                                    </TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                <TableRow v-for="item in formations" :key="item.id">
                                    <TableCell class="w-[40px]">
                                        <Checkbox class="bg-white scale-70" />
                                    </TableCell>
                                    <TableCell>
                                        {{ item.code }}
                                    </TableCell>
                                    <TableCell>
                                        {{ item.designation }}
                                    </TableCell>
                                    <TableCell>
                                        {{ item.duree }}
                                    </TableCell>
                                </TableRow>
                            </TableBody>
                        </Table>
                    </div>
                </div>
                <div class="">
                    <div class="flex items-center gap-3">
                        <Button size="md" variant="outline" class="rounded-full">
                            <span class="flex iconify hugeicons--pdf-02"></span>
                            Exporter pdf
                        </Button>
                        <Button size="md" variant="outline" class="rounded-full">
                            <span class="flex iconify hugeicons--ai-sheets"></span>
                            Exporter Excel
                        </Button>
                    </div>
                    <form action="" class="mt-6 flex flex-col gap-3">
                        <div class="flex flex-col space-y-1.5 flex-1">
                            <Label for="metier" class="text-sm font-medium">
                                Formation-métier
                            </Label>
                            <Select v-model="selectedMetier" id="metier">
                                <SelectTrigger id="metier" class="!h-10 bg-white w-full">
                                    <SelectValue placeholder="Sélectionner une formation-métier" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectGroup>
                                        <SelectItem v-for="m in metiers" :key="m.value" :value="m.value">
                                            {{ m.label }}
                                        </SelectItem>
                                    </SelectGroup>
                                </SelectContent>
                            </Select>
                        </div>
                        <div class="flex flex-col space-y-1.5">
                            <Label for="duree" class="text-sm font-medium">
                                Durée (mois)
                            </Label>
                            <Input type="number" id="duree" name="duree" v-model="duree" placeholder="Durée en mois"
                                class="w-full h-10 border border-gray-200/40 bg-white transition-all" min="1" />
                        </div>
                        <div class="mt-1 pb-2">
                            <p class="text-sm text-foreground-muted">
                                * Tous les champs sont obligatoires
                            </p>
                        </div>
                        <Button class="h-10 px-3">
                            <span class="iconify flex hugeicons--floppy-disk "></span>
                            Ajouter
                        </Button>
                    </form>
                </div>
            </div>
        </div>
    </DashLayout>
</template>
