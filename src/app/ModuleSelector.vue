<script lang="ts" setup>
import ModuleCard from "@/components/atoms/ModuleCard.vue"
import { modulesItems } from "@/data/navigations"
</script>

<template>
    <div class="relative">
        <div class="fixed inset-0 pointer-events-none select-none">
            <img src="/screen-pattern.png" alt="pattern background" class="size-full object-cover">
        </div>
        <div class="relative max-w-5xl mx-auto w-full py-6">
            <div class="flex justify-center mb-10">
                <img src="/pgfe-logo.png" width="500" class="h-32 w-auto" />
            </div>
            <div class="mb-4">
                <h2 class="text-2xl font-semibold text-fg-title uppercase mb-4">
                    Selectionnez le module
                </h2>
            </div>
            <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-5 lg:gap-8">
                <ModuleCard v-for="item in modulesItems" :key="item.id" :is-active="item.id === 1" v-bind="item" />

            </div>
        </div>
    </div>
</template>