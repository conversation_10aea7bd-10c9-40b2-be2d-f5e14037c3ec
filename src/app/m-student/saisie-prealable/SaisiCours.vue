<script setup lang="ts">
import DashLayout from '@/components/templates/DashLayout.vue';
import DashPageHeader from '@/components/templates/DashPageHeader.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { tagNavSaisie } from './tags-nav';

import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table'
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuGroup,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuPortal,
    DropdownMenuSeparator,
    DropdownMenuShortcut,
    DropdownMenuSub,
    DropdownMenuSubContent,
    DropdownMenuSubTrigger,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

import { Checkbox } from '@/components/ui/checkbox'
import NewCourse from '@/components/modals/NewCourse.vue';



</script>

<template>
    <DashLayout active-route="/apprenants/saisie-prealable" module-name="students">
        <div class="pb-6 mx-auto w-full max-w-6xl">
            <DashPageHeader title="Enseignement formel" :tags="tagNavSaisie" active-tag-name="cours" />
            <div class="mt-7 bg-white min-h-[72vh] rounded-3xl shadow-lg shadow-gray-100/20 p-3">
                <div class="bg-muted rounded-xl h-full min-h-[72vh] p-3">
                    <div class="flex items-center justify-between">
                        <div class="relative max-w-sm w-full">
                            <Input type="text" id="search" name="search" placeholder="Rechercher une filiere..."
                                class="w-full ps-10 border border-gray-200/40 bg-white transition-all h-10 rounded-full" />
                            <div class="absolute left-3 top-1/2 transform -translate-y-1/2 text-foreground-muted/70">
                                <span class="flex iconify hugeicons--search-01 text-sm"></span>
                            </div>
                        </div>
                        <div class="flex items-center gap-3">

                            <NewCourse/>
                            <Button size="md" variant="outline" class="">
                                <span class="flex iconify hugeicons--pdf-02"></span>
                                Exporter pdf
                            </Button>
                            <Button size="md" variant="outline" class="">
                                <span class="flex iconify hugeicons--ai-sheets"></span>
                                Exporter Excel
                            </Button>
                        </div>
                    </div>
                    <div class="rounded-xl overflow-hidden w-full mt-4">
                        <Table class="rounded-lg bg-white shadow-md">
                            <TableHeader class="bg-primary text-white text-xs uppercase">
                                <TableRow>
                                    <TableHead rowspan="2" class="w-14">
                                        <Checkbox :checked="true" class="bg-white scale-70" />
                                    </TableHead>
                                    <TableHead rowspan="2">Code</TableHead>
                                    <TableHead rowspan="2">Intitulé du cours</TableHead>
                                    <TableHead rowspan="2">Niveau</TableHead>
                                    <TableHead rowspan="2">Filière</TableHead>
                                    <TableHead rowspan="2">Titulaire</TableHead>
                                    <TableHead rowspan="2">Volume horaire</TableHead>
                                    <TableHead colspan="6" class="text-center px-4">Maximum</TableHead>
                                    <TableHead rowspan="2">Operations</TableHead>
                                </TableRow>
                                <TableRow class="bg-primary border-t-primary text-white text-xs uppercase">
                                    <TableHead class="px-4 py-2 !rounded-none">P1</TableHead>
                                    <TableHead class="px-4 py-2 !rounded-none">P2</TableHead>
                                    <TableHead class="px-4 py-2 !rounded-none">E1</TableHead>
                                    <TableHead class="px-4 py-2 !rounded-none">P3</TableHead>
                                    <TableHead class="px-4 py-2 !rounded-none">P4</TableHead>
                                    <TableHead class="px-4 py-2 !rounded-none">E2</TableHead>
                                </TableRow>
                            </TableHeader>

                            <TableBody>
                                <TableRow>
                                    <TableCell class="w-14">
                                        <Checkbox :checked="true" class="bg-white scale-70" />
                                    </TableCell>
                                    <TableCell>7300</TableCell>
                                    <TableCell>Autres articles similaires, non filetés, en fonte, fer ou acier
                                    </TableCell>
                                    <TableCell>2</TableCell>
                                    <TableCell>Letter</TableCell>
                                    <TableCell>Kalala</TableCell>
                                    <TableCell>50h</TableCell>
                                    <TableCell class="text-center px-4">73</TableCell>
                                    <TableCell class="text-center px-4">30</TableCell>
                                    <TableCell class="text-center px-4">68</TableCell>
                                    <TableCell class="text-center px-4">83</TableCell>
                                    <TableCell class="text-center px-4">76</TableCell>
                                    <TableCell class="text-center px-4">57</TableCell>
                                    <TableCell class="px-4">
                                        <div class="flex items-center gap-2">
                                            <Button variant="outline" size="icon" class="h-8 w-8">
                                                <span class="iconify hugeicons--edit-02"></span>
                                            </Button>
                                            <Button variant="destructive" size="icon" class="h-8 w-8">
                                                <span class="iconify hugeicons--delete-02"></span>
                                            </Button>
                                        </div>
                                    </TableCell>
                                </TableRow>

                                <TableRow>
                                    <TableCell class="w-14">
                                        <Checkbox :checked="true" class="bg-white scale-70" />
                                    </TableCell>
                                    <TableCell>7300</TableCell>
                                    <TableCell>Autres articles similaires, non filetés, en fonte, fer ou acier
                                    </TableCell>
                                    <TableCell>3</TableCell>
                                    <TableCell>Langues</TableCell>
                                    <TableCell>Kilolo</TableCell>
                                    <TableCell>30h</TableCell>
                                    <TableCell class="text-center px-4">73</TableCell>
                                    <TableCell class="text-center px-4">40</TableCell>
                                    <TableCell class="text-center px-4">67</TableCell>
                                    <TableCell class="text-center px-4">65</TableCell>
                                    <TableCell class="text-center px-4">76</TableCell>
                                    <TableCell class="text-center px-4">76</TableCell>
                                    <TableCell class="px-4">
                                        <div class="flex items-center gap-2">
                                            <Button variant="outline" size="icon" class="h-8 w-8">
                                                <span class="iconify hugeicons--edit-02"></span>
                                            </Button>
                                            <Button variant="destructive" size="icon" class="h-8 w-8">
                                                <span class="iconify hugeicons--delete-02"></span>
                                            </Button>
                                        </div>
                                    </TableCell>
                                </TableRow>

                                <TableRow>
                                    <TableCell class="w-14">
                                        <Checkbox :checked="false" class="bg-white scale-70" />
                                    </TableCell>
                                    <TableCell>7300</TableCell>
                                    <TableCell>Autres articles similaires, non filetés, en fonte, fer ou acier
                                    </TableCell>
                                    <TableCell>1</TableCell>
                                    <TableCell>Art</TableCell>
                                    <TableCell>Ngoie</TableCell>
                                    <TableCell>20h</TableCell>
                                    <TableCell class="text-center px-4">67</TableCell>
                                    <TableCell class="text-center px-4">75</TableCell>
                                    <TableCell class="text-center px-4">43</TableCell>
                                    <TableCell class="text-center px-4">40</TableCell>
                                    <TableCell class="text-center px-4">98</TableCell>
                                    <TableCell class="text-center px-4">70</TableCell>
                                    <TableCell class="px-4">
                                        <div class="flex items-center gap-2">
                                            <Button variant="outline" size="icon" class="h-8 w-8">
                                                <span class="iconify hugeicons--edit-02"></span>
                                            </Button>
                                            <Button variant="destructive" size="icon" class="h-8 w-8">
                                                <span class="iconify hugeicons--delete-02"></span>
                                            </Button>
                                        </div>
                                    </TableCell>
                                </TableRow>
                            </TableBody>
                        </Table>
                    </div>
                </div>

            </div>
        </div>
    </DashLayout>
</template>
