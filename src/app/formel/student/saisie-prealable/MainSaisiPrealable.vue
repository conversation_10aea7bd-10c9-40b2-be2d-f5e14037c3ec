<script setup lang="ts">
import FilliereItem from '@/components/atoms/FilliereItem.vue';
import DashLayout from '@/components/templates/DashLayout.vue';
import DashPageHeader from '@/components/templates/DashPageHeader.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { tagNavSaisie } from './tags-nav';



const filieres = [
    {
        id: 1,
        text: "Specialisations en arts"
    },
    {
        id: 2,
        text: "Lettres"
    },
    {
        id: 3,
        text: "Langues"
    },
]
</script>

<template>
    <DashLayout active-route="/apprenants/saisie-prealable" module-name="students">
        <div class="pb-6 mx-auto w-full max-w-6xl">
            <DashPageHeader title="Enseignement formel" :tags="tagNavSaisie" active-tag-name="filieres" />
            <div
                class="mt-7 bg-white min-h-[72vh] rounded-3xl shadow-lg shadow-gray-100/20 p-3 grid md:grid-cols-2 gap-4 md:gap-10">
                <div class="rounded-xl bg-muted p-2.5">
                    <div class="relative">
                        <Input type="text" id="search" name="search" placeholder="Rechercher une filiere..."
                            class="w-full ps-10 border border-gray-200/40 bg-white transition-all h-10 rounded-full" />
                        <div class="absolute left-3 top-1/2 transform -translate-y-1/2 text-foreground-muted/70">
                            <span class="flex iconify hugeicons--search-01 text-sm"></span>
                        </div>
                    </div>
                    <div class="mt-4">
                        <ul class="flex flex-col bg-white rounded-xl">
                            <FilliereItem v-for="item in filieres" :key="item.id" :title="item.text" :id="item.id" />
                        </ul>
                    </div>
                </div>
                <div class="">
                    <div class="flex items-center gap-3">
                        <Button size="md" variant="outline" class="rounded-full">
                            <span class="flex iconify hugeicons--pdf-02"></span>
                            Exporter pdf
                        </Button>
                        <Button size="md" variant="outline" class="rounded-full">
                            <span class="flex iconify hugeicons--ai-sheets"></span>
                            Exporter Excel
                        </Button>
                    </div>
                    <form action="" class="mt-6 flex items-end gap-3 w-full">
                        <div class="flex flex-col space-y-1.5 flex-1">
                            <Label for="filliere_name" class="text-sm font-medium">
                                Nom de la filière
                            </Label>

                            <Input type="text" id="filliere_name" name="filliere_name" placeholder="Lettres"
                                class="w-full h-10 border border-gray-200/40 bg-white transition-all" />
                        </div>
                        <Button class="h-10 px-3">
                            <span class="iconify flex hugeicons--floppy-disk "></span>
                            Ajouter
                        </Button>
                    </form>
                </div>
            </div>
        </div>
    </DashLayout>
</template>
