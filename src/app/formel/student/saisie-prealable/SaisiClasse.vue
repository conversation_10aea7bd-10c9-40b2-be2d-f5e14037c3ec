<script setup lang="ts">
import DashLayout from '@/components/templates/DashLayout.vue';
import DashPageHeader from '@/components/templates/DashPageHeader.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { tagNavSaisie } from './tags-nav';
import {
    Table,
    TableBody,
    TableCaption,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table'
import { Checkbox } from '@/components/ui/checkbox';
import {
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select'



const classes = [
    {
        id: 1,
        code: "C1",
        designation: "Classe 1",
        niveau: "1",
        filiere: "Lettres",
        cycle: "Cycle 1"
    },
    {
        id: 2,
        code: "C2",
        designation: "Classe 2",
        niveau: "2",
        filiere: "Lettres",
        cycle: "Cycle 1"
    },
    {
        id: 3,
        code: "C3",
        designation: "Classe 3",
        niveau: "3",
        filiere: "Lettres",
        cycle: "Cycle 1"
    },
]
</script>

<template>
    <DashLayout active-route="/apprenants/saisie-prealable" module-name="students">
        <div class="pb-6 mx-auto w-full max-w-6xl">
            <DashPageHeader title="Enseignement formel" :tags="tagNavSaisie" active-tag-name="classes" />
            <div
                class="mt-7 bg-white min-h-[72vh] rounded-3xl shadow-lg shadow-gray-100/20 p-3 grid md:grid-cols-[1fr_250px] xl:grid-cols-[1fr_320px] gap-4 md:gap-10">
                <div class="rounded-xl bg-muted p-2.5">
                    <div class="relative max-w-sm">
                        <Input type="text" id="search" name="search" placeholder="Rechercher une filiere..."
                            class="w-full ps-10 border border-gray-200/40 bg-white transition-all h-10 rounded-full" />
                        <div class="absolute left-3 top-1/2 transform -translate-y-1/2 text-foreground-muted/70">
                            <span class="flex iconify hugeicons--search-01 text-sm"></span>
                        </div>
                    </div>
                    <div class="mt-4 rounded-xl overflow-hidden">
                        <Table class="rounded-xl bg-white">
                            <TableHeader>
                                <TableRow>
                                    <TableHead class="w-[20px]">
                                        <Checkbox class="bg-white scale-70" />
                                    </TableHead>
                                    <TableHead class="text-left">
                                        Code
                                    </TableHead>
                                    <TableHead>
                                        Designation
                                    </TableHead>
                                    <TableHead>
                                        Niveau
                                    </TableHead>
                                    <TableHead>
                                        Filière
                                    </TableHead>
                                    <TableHead>
                                        Cycle
                                    </TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                <TableRow v-for="item in classes" :key="item.id">
                                    <TableCell class="w-[40px]">
                                        <Checkbox class="bg-white scale-70" />
                                    </TableCell>
                                    <TableCell>
                                        {{ item.code }}
                                    </TableCell>
                                    <TableCell>
                                        {{ item.designation }}
                                    </TableCell>
                                    <TableCell class="text-left">
                                        {{ item.niveau }}
                                    </TableCell>
                                    <TableCell>
                                        {{ item.filiere }}
                                    </TableCell>
                                    <TableCell>
                                        {{ item.cycle }}
                                    </TableCell>
                                </TableRow>
                            </TableBody>
                        </Table>
                    </div>
                </div>
                <div class="">
                    <div class="flex items-center gap-3">
                        <Button size="md" variant="outline" class="rounded-full">
                            <span class="flex iconify hugeicons--pdf-02"></span>
                            Exporter pdf
                        </Button>
                        <Button size="md" variant="outline" class="rounded-full">
                            <span class="flex iconify hugeicons--ai-sheets"></span>
                            Exporter Excel
                        </Button>
                    </div>
                    <form action="" class="mt-6 flex flex-col gap-3">
                        <div class="flex flex-col space-y-1.5 flex-1">
                            <Label for="niveau" class="text-sm font-medium">
                                Niveau
                            </Label>
                            <Select id="niveau">
                                <SelectTrigger id="niveau" class="h-10 w-full">
                                    <SelectValue placeholder="Select a verified email to display" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectGroup>
                                        <SelectItem value="<EMAIL>">
                                            <EMAIL>
                                        </SelectItem>
                                        <SelectItem value="<EMAIL>">
                                            <EMAIL>
                                        </SelectItem>
                                        <SelectItem value="<EMAIL>">
                                            <EMAIL>
                                        </SelectItem>
                                    </SelectGroup>
                                </SelectContent>
                            </Select>
                        </div>
                        <div class="flex flex-col space-y-1.5">
                            <Label for="filliere_name" class="text-sm font-medium">
                                Désignation
                            </Label>
                            <Input type="text" id="filliere_name" name="filliere_name" placeholder="Lettres"
                                class="w-full h-10 border border-gray-200/40 bg-white transition-all" />
                        </div>
                        <div class="flex flex-col space-y-1.5 flex-1">
                            <Label for="niveau" class="text-sm font-medium">
                                Filière
                            </Label>
                            <Select id="niveau">
                                <SelectTrigger id="niveau" class="h-10 w-full">
                                    <SelectValue placeholder="Select a verified email to display" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectGroup>
                                        <SelectItem value="<EMAIL>">
                                            <EMAIL>
                                        </SelectItem>
                                        <SelectItem value="<EMAIL>">
                                            <EMAIL>
                                        </SelectItem>
                                        <SelectItem value="<EMAIL>">
                                            <EMAIL>
                                        </SelectItem>
                                    </SelectGroup>
                                </SelectContent>
                            </Select>
                        </div>
                        <div class="flex flex-col space-y-1.5 flex-1">
                            <Label for="niveau" class="text-sm font-medium">
                                Cycle
                            </Label>
                            <Select id="niveau">
                                <SelectTrigger id="niveau" class="h-10 w-full">
                                    <SelectValue placeholder="Select a verified email to display" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectGroup>
                                        <SelectItem value="<EMAIL>">
                                            <EMAIL>
                                        </SelectItem>
                                        <SelectItem value="<EMAIL>">
                                            <EMAIL>
                                        </SelectItem>
                                        <SelectItem value="<EMAIL>">
                                            <EMAIL>
                                        </SelectItem>
                                    </SelectGroup>
                                </SelectContent>
                            </Select>
                        </div>
                        <div class="mt-1 pb-2">
                            <p class="text-sm text-foreground-muted">
                               * Tous les champs sont obligatoires
                            </p>
                        </div>
                        <Button class="h-10 px-3">
                            <span class="iconify flex hugeicons--floppy-disk "></span>
                            Ajouter
                        </Button>
                    </form>
                </div>
            </div>
        </div>
    </DashLayout>
</template>
