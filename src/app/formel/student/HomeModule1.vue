<script setup lang="ts">
import CardDashStat from '@/components/molecules/CardDashStat.vue';
import DashLayout from '@/components/templates/DashLayout.vue';


</script>

<template>
    <DashLayout active-route="/apprenants" module-name="students">
        <div class="py-20 mx-auto w-full max-w-6xl">
            <div class="grid sm:grid-cols-2 lg:grid-cols-4 gap-6">
                <CardDashStat icon="hugeicons--user" :value="10000" title="Apprenants"
                    description="Total des apprenants" color="#008080" />
                <CardDashStat icon="hugeicons--user" :value="10000" title="Apprenants"
                    description="Total des apprenants" color="#008080" />
                <CardDashStat icon="hugeicons--user" :value="10000" title="Apprenants"
                    description="Total des apprenants" color="#008080" />
                <CardDashStat icon="hugeicons--user" :value="10000" title="Apprenants"
                    description="Total des apprenants" color="#008080" />
            </div>
            <div class="mt-10 bg-white rounded-3xl shadow-lg shadow-gray-100/20 p-3">

            </div>
        </div>
    </DashLayout>
</template>
