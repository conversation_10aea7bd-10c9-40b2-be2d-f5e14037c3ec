<script setup lang="ts">
import DashLayout from '@/components/templates/DashLayout.vue';
import DashPageHeader from '@/components/templates/DashPageHeader.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { tagNavSaisieOperations } from './tags-nav';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table'
import { Checkbox } from '@/components/ui/checkbox';

// Nouvelle structure pour les étudiants
const students = [
    {
        id: 1,
        nom: "Mwamba",
        postnom: "Kabasele",
        prenom: "Jean",
        sexe: "M",
        filiere: "Lettres",
        classe: "1ère",
        annee: "2024-2025",
        cycle: "Cycle 1"
    },
    {
        id: 2,
        nom: "Kasongo",
        postnom: "<PERSON><PERSON><PERSON>",
        prenom: "<PERSON>",
        sexe: "F",
        filiere: "Sciences",
        classe: "2ème",
        annee: "2024-2025",
        cycle: "Cycle 1"
    },
    {
        id: 3,
        nom: "Ilunga",
        postnom: "Kabeya",
        prenom: "Patrick",
        sexe: "M",
        filiere: "Mathématiques",
        classe: "3ème",
        annee: "2024-2025",
        cycle: "Cycle 2"
    },
    {
        id: 4,
        nom: "Tshibanda",
        postnom: "Mbuyi",
        prenom: "Aline",
        sexe: "F",
        filiere: "Lettres",
        classe: "4ème",
        annee: "2024-2025",
        cycle: "Cycle 2"
    },
]
</script>

<template>
    <DashLayout active-route="/apprenants/saisie-prealable" module-name="students">
        <div class="pb-6 mx-auto w-full max-w-6xl">
            <DashPageHeader title="Opérations enseignement formel" :tags="tagNavSaisieOperations"
                active-tag-name="inscriptions" />
            <div class="mt-7 bg-white rounded-3xl shadow-lg shadow-gray-100/20 p-3">
                <div class="rounded-xl min-h-[72vh] bg-muted p-2.5">
                     <div class="flex items-center justify-between">
                        <div class="relative max-w-sm w-full">
                            <Input type="text" id="search" name="search" placeholder="Rechercher une filiere..."
                                class="w-full ps-10 border border-gray-200/40 bg-white transition-all h-10 rounded-full" />
                            <div class="absolute left-3 top-1/2 transform -translate-y-1/2 text-foreground-muted/70">
                                <span class="flex iconify hugeicons--search-01 text-sm"></span>
                            </div>
                        </div>
                        <div class="flex items-center gap-3">
                            <Button size="md" class="">
                                <span class="flex iconify hugeicons--plus-sign"></span>
                                Ajouter
                            </Button>
                            <Button size="md" variant="outline" class="">
                                <span class="flex iconify hugeicons--pdf-02"></span>
                                Exporter pdf
                            </Button>
                            <Button size="md" variant="outline" class="">
                                <span class="flex iconify hugeicons--ai-sheets"></span>
                                Exporter Excel
                            </Button>
                        </div>
                    </div>
                    <div class="mt-4 rounded-xl overflow-hidden">
                        <Table class="rounded-xl bg-white">
                            <TableHeader>
                                <TableRow>
                                    <TableHead class="w-[20px]">
                                        <Checkbox class="bg-white scale-70" />
                                    </TableHead>
                                    <TableHead>Nom</TableHead>
                                    <TableHead>Postnom</TableHead>
                                    <TableHead>Prénom</TableHead>
                                    <TableHead>Sexe</TableHead>
                                    <TableHead>Filière</TableHead>
                                    <TableHead>Classe</TableHead>
                                    <TableHead>Année scolaire</TableHead>
                                    <TableHead>Cycle</TableHead>
                                    <TableHead>
                                        Operations
                                    </TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                <TableRow v-for="item in students" :key="item.id">
                                    <TableCell class="w-[40px]">
                                        <Checkbox class="bg-white scale-70" />
                                    </TableCell>
                                    <TableCell>{{ item.nom }}</TableCell>
                                    <TableCell>{{ item.postnom }}</TableCell>
                                    <TableCell>{{ item.prenom }}</TableCell>
                                    <TableCell>{{ item.sexe }}</TableCell>
                                    <TableCell>{{ item.filiere }}</TableCell>
                                    <TableCell>{{ item.classe }}</TableCell>
                                    <TableCell>{{ item.annee }}</TableCell>
                                    <TableCell>{{ item.cycle }}</TableCell>
                                    <TableCell>
                                        <div class="flex items-center gap-2 w-max">
                                            <Button variant="outline" size="icon" class="size-8">
                                                <span class="iconify hugeicons--edit-02"></span>
                                            </Button>
                                            <Button variant="destructive" size="icon" class="size-8">
                                                <span class="iconify hugeicons--delete-02"></span>
                                            </Button>
                                        </div>
                                    </TableCell>
                                </TableRow>
                            </TableBody>
                        </Table>
                    </div>
                </div>
            </div>
        </div>
    </DashLayout>
</template>
