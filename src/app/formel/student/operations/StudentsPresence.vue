<script setup lang="ts">
import DashLayout from '@/components/templates/DashLayout.vue';
import DashPageHeader from '@/components/templates/DashPageHeader.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { tagNavSaisieOperations } from './tags-nav';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table'
import { Checkbox } from '@/components/ui/checkbox';
import { Switch } from '@/components/ui/switch';
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from '@/components/ui/popover'




const classes = [
    {
        id: 1,
        code: "C1",
        designation: "Classe 1",
        niveau: "1",
        filiere: "Lettres",
        cycle: "Cycle 1"
    },
    {
        id: 2,
        code: "C2",
        designation: "Classe 2",
        niveau: "2",
        filiere: "Lettres",
        cycle: "Cycle 1"
    },
    {
        id: 3,
        code: "C3",
        designation: "Classe 3",
        niveau: "3",
        filiere: "Lettres",
        cycle: "Cycle 1"
    },
]
</script>

<template>
    <DashLayout active-route="/apprenants/saisie-prealable" module-name="students">
        <div class="pb-6 mx-auto w-full max-w-6xl">
            <DashPageHeader title="Opérations enseignement formel" :tags="tagNavSaisieOperations"
                active-tag-name="presences" />
            <div class="mt-7 bg-white min-h-[72vh] rounded-3xl shadow-lg shadow-gray-100/20 p-3">
                <div class="rounded-xl bg-muted p-2.5">
                    <div class="flex items-center justify-between">
                        <div class="flex flex-1 items-center">
                            <div class="relative max-w-xs w-full">
                                <Input type="text" id="search" name="search" placeholder="Rechercher une filiere..."
                                    class="w-full ps-10 border border-gray-200/40 bg-white transition-all h-10 rounded-full" />
                                <div
                                    class="absolute left-3 top-1/2 transform -translate-y-1/2 text-foreground-muted/70">
                                    <span class="flex iconify hugeicons--search-01 text-sm"></span>
                                </div>
                            </div>
                            <Popover>
                                <PopoverTrigger as-child>
                                    <Button variant="outline">
                                        Open popover
                                    </Button>
                                </PopoverTrigger>
                                <PopoverContent class="w-80">
                                    <div class="grid gap-4">
                                        <div class="space-y-2">
                                            <h4 class="font-medium leading-none">
                                                Dimensions
                                            </h4>
                                            <p class="text-sm text-muted-foreground">
                                                Set the dimensions for the layer.
                                            </p>
                                        </div>
                                        <div class="grid gap-2">
                                            <div class="grid grid-cols-3 items-center gap-4">
                                                <Label for="width">Width</Label>
                                                <Input id="width" type="text" default-value="100%"
                                                    class="col-span-2 h-8" />
                                            </div>
                                            <div class="grid grid-cols-3 items-center gap-4">
                                                <Label for="maxWidth">Max. width</Label>
                                                <Input id="maxWidth" type="text" default-value="300px"
                                                    class="col-span-2 h-8" />
                                            </div>
                                            <div class="grid grid-cols-3 items-center gap-4">
                                                <Label for="height">Height</Label>
                                                <Input id="height" type="text" default-value="25px"
                                                    class="col-span-2 h-8" />
                                            </div>
                                            <div class="grid grid-cols-3 items-center gap-4">
                                                <Label for="maxHeight">Max. height</Label>
                                                <Input id="maxHeight" type="text" default-value="none"
                                                    class="col-span-2 h-8" />
                                            </div>
                                        </div>
                                    </div>
                                </PopoverContent>
                            </Popover>
                        </div>
                        <div class="flex items-center gap-3">
                            <div>
                                <Label for="annee">Année scolaire</Label>
                                <select id="annee" class="border rounded px-2 py-1 text-sm bg-white">
                                    <option>2023-2024</option>
                                    <option>2024-2025</option>
                                </select>
                            </div>
                            <div>
                                <Label for="filiere">Filière</Label>
                                <select id="filiere" class="border rounded px-2 py-1 text-sm bg-white">
                                    <option>Lettres</option>
                                    <option>Sciences</option>
                                </select>
                            </div>
                            <div>
                                <Label for="classe">Classe</Label>
                                <select id="classe" class="border rounded px-2 py-1 text-sm bg-white">
                                    <option>Classe 1</option>
                                    <option>Classe 2</option>
                                    <option>Classe 3</option>
                                </select>
                            </div>
                            <div>
                                <Label for="date">Date</Label>
                                <Input id="date" type="date" class="border rounded px-2 py-1 text-sm bg-white" />
                            </div>
                        </div>
                        <div class="flex items-center gap-3">
                            <Button size="md" class="">
                                <span class="flex iconify hugeicons--plus-sign"></span>
                                Ajouter
                            </Button>
                            <Button size="md" variant="outline" class="">
                                <span class="flex iconify hugeicons--pdf-02"></span>
                                Exporter pdf
                            </Button>
                            <Button size="md" variant="outline" class="">
                                <span class="flex iconify hugeicons--ai-sheets"></span>
                                Exporter Excel
                            </Button>
                        </div>
                    </div>
                    <div class="mt-4 rounded-xl overflow-hidden">
                        <Table class="rounded-xl bg-white">
                            <TableHeader>
                                <TableRow>
                                    <TableHead class="w-[20px]">
                                        <Checkbox class="bg-white scale-70" />
                                    </TableHead>
                                    <TableHead>
                                        Apprenant
                                    </TableHead>
                                    <TableHead>
                                        Presence
                                    </TableHead>
                                    <TableHead>
                                        Commentaire
                                    </TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                <TableRow v-for="item in classes" :key="item.id">
                                    <TableCell class="w-[40px]">
                                        <Checkbox class="bg-white scale-70" />
                                    </TableCell>
                                    <TableCell>
                                        {{ item.code }}
                                    </TableCell>
                                    <TableCell>
                                        <Switch id="airplane-mode" />
                                    </TableCell>
                                    <TableCell class="text-left">
                                        {{ item.niveau }}
                                    </TableCell>
                                </TableRow>
                            </TableBody>
                        </Table>
                    </div>
                </div>
            </div>
        </div>
    </DashLayout>
</template>
